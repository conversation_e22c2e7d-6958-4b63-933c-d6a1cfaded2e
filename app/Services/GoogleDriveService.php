<?php

namespace App\Services;

use Google\Client as Google_Client;
use Google\Service\Drive as Google_Service_Drive;
use Google\Service\Drive\DriveFile as Google_Service_Drive_DriveFile;

class GoogleDriveService
{
    protected Google_Service_Drive $drive;

    // Set this to your uploaded folder ID or leave null to upload to root
    protected ?string $parentFolderId = null;

    // Set this to your shared drive ID to upload to shared drive
    protected ?string $sharedDriveId = null;

    public function __construct()
    {
        $client = new Google_Client();
        $client->setAuthConfig(storage_path('client_secret_backup.json')); // Update path if needed
        $client->addScope(Google_Service_Drive::DRIVE); // Need full drive access for shared drives

        $this->drive = new Google_Service_Drive($client);

        // Set your shared drive ID here - you need to create a shared drive first
        // and get its ID from the Google Drive web interface
        $this->sharedDriveId = env('GOOGLE_DRIVE_SHARED_DRIVE_ID');
    }

    /**
     * Uploads a file to Google Drive
     *
     * @param string $filePath Local file path
     * @param string $fileName Name to use on Google Drive
     * @return string|null Uploaded file ID or null on failure
     */
    public function upload(string $filePath, string $fileName): ?string
    {
        $fileMetadata = new Google_Service_Drive_DriveFile([
            'name' => $fileName,
            'parents' => $this->parentFolderId ? [$this->parentFolderId] : []
        ]);

        try {
            $content = file_get_contents($filePath);

            $optParams = [
                'data' => $content,
                'mimeType' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'uploadType' => 'multipart',
                'fields' => 'id'
            ];

            // If using shared drive, add the supportsAllDrives parameter
            if ($this->sharedDriveId) {
                $optParams['supportsAllDrives'] = true;
            }

            $file = $this->drive->files->create($fileMetadata, $optParams);

            return $file->id;
        } catch (\Exception $e) {
            logger()->error('Google Drive Upload Error: ' . $e->getMessage());
            return $e->getMessage();
        }
    }

    /**
     * Optionally allow setting a parent folder ID
     */
    public function setParentFolder(string $folderId): void
    {
        $this->parentFolderId = $folderId;
    }

    /**
     * Create a folder and return its ID
     *
     * @param string $folderName
     * @param string|null $parentId
     * @return string|null
     */
    public function createFolder(string $folderName, ?string $parentId = null): ?string
    {
        $folderMetadata = new Google_Service_Drive_DriveFile([
            'name' => $folderName,
            'mimeType' => 'application/vnd.google-apps.folder',
        ]);

        // Use shared drive as parent if no specific parent is provided
        if ($parentId) {
            $folderMetadata->setParents([$parentId]);
        } elseif ($this->sharedDriveId) {
            $folderMetadata->setParents([$this->sharedDriveId]);
        }

        try {
            $optParams = ['fields' => 'id'];

            // If using shared drive, add the supportsAllDrives parameter
            if ($this->sharedDriveId) {
                $optParams['supportsAllDrives'] = true;
            }

            $folder = $this->drive->files->create($folderMetadata, $optParams);
            return $folder->id;
        } catch (\Exception $e) {
            dump($e->getMessage());
            logger()->error('Google Drive Folder Creation Error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Set the shared drive ID to use for uploads
     */
    public function setSharedDrive(string $sharedDriveId): void
    {
        $this->sharedDriveId = $sharedDriveId;
    }
}