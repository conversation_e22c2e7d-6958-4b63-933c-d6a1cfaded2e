<?php

namespace App\Console\Commands;

use App\Services\GoogleDriveService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Laravel\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class CopyDataProductionAsFileCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'copy-data-as-file:production';

    /**
     * The console command description.
     * @var string
     */
    protected $description = 'Export table data as Excel files and save to S3';

    /**
     * The tables that should be exported
     * @var array
     */
    protected $tables = [
        'products',
        'product_stocks',
        'variances',
        'variances_stocks',
        'stocks',
    ];


    public function handle()
    {
        $this->info('Exporting data from production tables to S3 as Excel...');

        // Create a folder with timestamp
        $timestamp = date('Y-m-d');
        $folderPath = "csc_exports/{$timestamp}";

        // Create a new spreadsheet
        $spreadsheet = new Spreadsheet();

        // Remove the default worksheet
        $spreadsheet->removeSheetByIndex(0);

        foreach ($this->tables as $index => $table) {
            $this->info("Processing table: {$table}");

            // Get all data from the table
            $rows = DB::table($table)->get();

            if ($rows->isEmpty()) {
                $this->warn("No data found in table: {$table}");
                continue;
            }

            // Get table structure - ensure columns are in the correct order
            $columns = $this->getOrderedColumns($table);

            // Create a new worksheet for this table
            $worksheet = $spreadsheet->createSheet();
            $worksheet->setTitle(substr($table, 0, 31)); // Excel limits sheet names to 31 chars

            // Add column headers
            foreach ($columns as $colIndex => $column) {
                $worksheet->setCellValueByColumnAndRow($colIndex + 1, 1, $column);
            }

            // Add data rows
            $rowIndex = 2;
            foreach ($rows as $row) {
                foreach ($columns as $colIndex => $column) {
                    $value = $row->$column ?? null;

                    // Handle JSON fields
                    if (is_object($value) || is_array($value)) {
                        $value = json_encode($value);
                    }


                    $worksheet->setCellValueByColumnAndRow($colIndex + 1, $rowIndex, $value);
                }
                $rowIndex++;
            }
        }

        // Save the Excel file to a temporary location
        $writer = new Xlsx($spreadsheet);
        $tempFile = tempnam(sys_get_temp_dir(), 'export_');
        $writer->save($tempFile);

        // Upload to S3
        $filePath = "{$folderPath}/data_export_{$timestamp}.xlsx";

        $googleDriveService = new GoogleDriveService();

        $driveFolderId = $googleDriveService->createFolder($folderPath);

        if ($driveFolderId) {
            $googleDriveService->setParentFolder($driveFolderId);
        }


        $this->info($googleDriveService->upload($tempFile, "data_export_{$timestamp}.xlsx"));


        Storage::disk('s3')->put($filePath, file_get_contents($tempFile));

        unlink($tempFile);

        $this->info("All tables exported successfully to S3: {$filePath}");
        return 0;
    }

    /**
     * Get columns in the correct order from the database
     *
     * @param string $table
     * @return array
     */
    protected function getOrderedColumns(string $table): array
    {
        // Get columns in the order they appear in the database
        $columns = DB::select("SHOW COLUMNS FROM `{$table}`");
        return collect($columns)->pluck('Field')->toArray();
    }
}